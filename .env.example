# Environment Configuration for Generative Art Platform

# Flask Configuration
SECRET_KEY=your-secret-key-here-change-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///generative_art.db
# For PostgreSQL: postgresql://username:password@localhost/generative_art

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes
UPLOAD_FOLDER=uploads
GENERATED_FOLDER=generated

# AI Model Configuration
TORCH_DEVICE=cpu  # or 'cuda' if GPU available
MODEL_CACHE_DIR=models

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:5000

# Logging
LOG_LEVEL=INFO

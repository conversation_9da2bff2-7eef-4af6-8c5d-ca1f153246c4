from flask import Blueprint, request, jsonify, session, current_app, send_file
from werkzeug.utils import secure_filename
from models import User, Artwork, Style, Like, Comment, db
# Try to import full AI models, fallback to simple ones
try:
    from ai_models import get_style_transfer_model, get_gan_model
except ImportError:
    from ai_models_simple import get_style_transfer_model, get_gan_model
import os
import uuid
import threading
import time
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def require_auth(f):
    """Decorator to require authentication"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@api_bp.route('/styles', methods=['GET'])
def get_styles():
    """Get all available styles"""
    try:
        styles = Style.query.filter_by(is_active=True).all()
        return jsonify({
            'styles': [style.to_dict() for style in styles]
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching styles: {e}")
        return jsonify({'error': 'Failed to fetch styles'}), 500

@api_bp.route('/upload', methods=['POST'])
@require_auth
def upload_image():
    """Upload content image for processing"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
            
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
            
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type'}), 400
            
        # Generate unique filename
        filename = str(uuid.uuid4()) + '.' + file.filename.rsplit('.', 1)[1].lower()
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        
        # Save file
        file.save(filepath)
        
        return jsonify({
            'message': 'File uploaded successfully',
            'filename': filename,
            'filepath': filepath
        }), 200
        
    except Exception as e:
        logger.error(f"Error uploading file: {e}")
        return jsonify({'error': 'Failed to upload file'}), 500

@api_bp.route('/generate', methods=['POST'])
@require_auth
def generate_artwork():
    """Generate artwork using style transfer or GAN"""
    try:
        data = request.get_json()
        user_id = session['user_id']
        
        # Validate required fields
        if not data or 'generation_type' not in data:
            return jsonify({'error': 'Generation type is required'}), 400
            
        generation_type = data['generation_type']
        
        if generation_type not in ['style_transfer', 'gan']:
            return jsonify({'error': 'Invalid generation type'}), 400
            
        # Create artwork record
        artwork = Artwork(
            title=data.get('title', f'Generated Art {datetime.utcnow().strftime("%Y%m%d_%H%M%S")}'),
            description=data.get('description', ''),
            user_id=user_id,
            generation_type=generation_type,
            parameters=data.get('parameters', {}),
            processing_status='pending'
        )
        
        if generation_type == 'style_transfer':
            if 'content_image' not in data or 'style_id' not in data:
                return jsonify({'error': 'Content image and style are required for style transfer'}), 400
                
            artwork.original_image_path = data['content_image']
            artwork.style_id = data['style_id']
            
        elif generation_type == 'gan':
            artwork.style_id = data.get('style_id')  # Optional for GAN
            
        db.session.add(artwork)
        db.session.commit()
        
        # Start background processing
        thread = threading.Thread(
            target=process_artwork_async,
            args=(artwork.id, generation_type, data)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'message': 'Artwork generation started',
            'artwork_id': artwork.id,
            'status': 'pending'
        }), 202
        
    except Exception as e:
        logger.error(f"Error generating artwork: {e}")
        db.session.rollback()
        return jsonify({'error': 'Failed to start artwork generation'}), 500

def process_artwork_async(artwork_id, generation_type, data):
    """Process artwork generation in background"""
    try:
        with current_app.app_context():
            artwork = Artwork.query.get(artwork_id)
            if not artwork:
                return
                
            artwork.processing_status = 'processing'
            db.session.commit()
            
            start_time = time.time()
            
            # Generate output filename
            output_filename = f"{artwork_id}.jpg"
            output_path = os.path.join(current_app.config['GENERATED_FOLDER'], output_filename)
            
            success = False
            
            if generation_type == 'style_transfer':
                # Get style image path
                style = Style.query.get(artwork.style_id)
                if style:
                    model = get_style_transfer_model()
                    success = model.style_transfer(
                        content_path=artwork.original_image_path,
                        style_path=style.style_image_path,
                        output_path=output_path,
                        num_steps=data.get('parameters', {}).get('steps', 300),
                        style_weight=data.get('parameters', {}).get('style_weight', 1000000),
                        content_weight=data.get('parameters', {}).get('content_weight', 1)
                    )
                    
            elif generation_type == 'gan':
                model = get_gan_model()
                seed = data.get('parameters', {}).get('seed')
                success = model.generate_art(output_path, seed=seed)
            
            # Update artwork record
            processing_time = time.time() - start_time
            artwork.processing_time = processing_time
            
            if success:
                artwork.generated_image_path = output_path
                artwork.processing_status = 'completed'
                logger.info(f"Artwork {artwork_id} processed successfully in {processing_time:.2f}s")
            else:
                artwork.processing_status = 'failed'
                logger.error(f"Artwork {artwork_id} processing failed")
                
            db.session.commit()
            
    except Exception as e:
        logger.error(f"Error processing artwork {artwork_id}: {e}")
        try:
            with current_app.app_context():
                artwork = Artwork.query.get(artwork_id)
                if artwork:
                    artwork.processing_status = 'failed'
                    db.session.commit()
        except:
            pass

@api_bp.route('/artwork/<artwork_id>/status', methods=['GET'])
def get_artwork_status(artwork_id):
    """Get artwork processing status"""
    try:
        artwork = Artwork.query.get(artwork_id)
        
        if not artwork:
            return jsonify({'error': 'Artwork not found'}), 404
            
        return jsonify({
            'artwork_id': artwork_id,
            'status': artwork.processing_status,
            'processing_time': artwork.processing_time
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting artwork status: {e}")
        return jsonify({'error': 'Failed to get artwork status'}), 500

@api_bp.route('/artwork/<artwork_id>', methods=['GET'])
def get_artwork(artwork_id):
    """Get artwork details"""
    try:
        artwork = Artwork.query.get(artwork_id)
        
        if not artwork:
            return jsonify({'error': 'Artwork not found'}), 404
            
        # Check if artwork is public or user owns it
        user_id = session.get('user_id')
        if not artwork.is_public and artwork.user_id != user_id:
            return jsonify({'error': 'Access denied'}), 403
            
        # Increment view count
        artwork.view_count += 1
        db.session.commit()
        
        return jsonify({'artwork': artwork.to_dict()}), 200
        
    except Exception as e:
        logger.error(f"Error getting artwork: {e}")
        return jsonify({'error': 'Failed to get artwork'}), 500

@api_bp.route('/artwork/<artwork_id>/like', methods=['POST'])
@require_auth
def like_artwork(artwork_id):
    """Like or unlike artwork"""
    try:
        user_id = session['user_id']
        artwork = Artwork.query.get(artwork_id)
        
        if not artwork:
            return jsonify({'error': 'Artwork not found'}), 404
            
        # Check if already liked
        existing_like = Like.query.filter_by(
            user_id=user_id,
            artwork_id=artwork_id
        ).first()
        
        if existing_like:
            # Unlike
            db.session.delete(existing_like)
            artwork.like_count = max(0, artwork.like_count - 1)
            action = 'unliked'
        else:
            # Like
            like = Like(user_id=user_id, artwork_id=artwork_id)
            db.session.add(like)
            artwork.like_count += 1
            action = 'liked'
            
        db.session.commit()
        
        return jsonify({
            'message': f'Artwork {action}',
            'like_count': artwork.like_count,
            'action': action
        }), 200
        
    except Exception as e:
        logger.error(f"Error liking artwork: {e}")
        db.session.rollback()
        return jsonify({'error': 'Failed to like artwork'}), 500

@api_bp.route('/image/<path:filename>')
def serve_image(filename):
    """Serve uploaded or generated images"""
    try:
        # Check in uploads folder first
        upload_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        if os.path.exists(upload_path):
            return send_file(upload_path)
            
        # Check in generated folder
        generated_path = os.path.join(current_app.config['GENERATED_FOLDER'], filename)
        if os.path.exists(generated_path):
            return send_file(generated_path)
            
        return jsonify({'error': 'Image not found'}), 404
        
    except Exception as e:
        logger.error(f"Error serving image {filename}: {e}")
        return jsonify({'error': 'Failed to serve image'}), 500

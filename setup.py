#!/usr/bin/env python3
"""
Setup script for Generative Art Platform
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    try:
        # Install basic dependencies first
        basic_deps = [
            'flask',
            'flask-sqlalchemy',
            'flask-migrate',
            'flask-cors',
            'pillow',
            'numpy'
        ]
        
        for dep in basic_deps:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
        
        print("✓ Basic dependencies installed")
        
        # Try to install ML dependencies (may fail on some systems)
        ml_deps = [
            'torch',
            'torchvision'
        ]
        
        for dep in ml_deps:
            try:
                print(f"Installing {dep}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
                print(f"✓ {dep} installed")
            except subprocess.CalledProcessError:
                print(f"⚠ Failed to install {dep} - you may need to install manually")
        
        return True
        
    except Exception as e:
        print(f"Error installing dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("Creating directories...")
    
    directories = [
        'uploads',
        'generated',
        'static/styles'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created {directory}")

def main():
    """Main setup function"""
    print("=" * 50)
    print("GENERATIVE ART PLATFORM SETUP")
    print("=" * 50)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    if install_dependencies():
        print("\n✓ Setup completed successfully!")
        print("\nNext steps:")
        print("1. Run: python init_db.py")
        print("2. Run: python app.py")
        print("3. Open: http://localhost:5000")
    else:
        print("\n✗ Setup failed. Please install dependencies manually:")
        print("pip install -r requirements.txt")
    
    print("=" * 50)

if __name__ == '__main__':
    main()

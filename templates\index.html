<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generative Art Platform</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#"><i class="fas fa-palette"></i> ArtGen</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#create">Create</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#gallery">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#trending">Trending</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item" id="auth-section">
                        <button class="btn btn-outline-light" onclick="showAuthModal()">Login</button>
                    </li>
                    <li class="nav-item dropdown d-none" id="user-menu">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <span id="username-display"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#profile">Profile</a></li>
                            <li><a class="dropdown-item" href="#my-artworks">My Artworks</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Create Section -->
        <div id="create-section" class="section active">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-magic"></i> Create AI Art</h4>
                        </div>
                        <div class="card-body">
                            <!-- Generation Type Selection -->
                            <div class="mb-3">
                                <label class="form-label">Generation Type</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="generation_type" id="style_transfer" value="style_transfer" checked>
                                    <label class="btn btn-outline-primary" for="style_transfer">Style Transfer</label>
                                    
                                    <input type="radio" class="btn-check" name="generation_type" id="gan" value="gan">
                                    <label class="btn btn-outline-primary" for="gan">GAN Art</label>
                                </div>
                            </div>

                            <!-- Content Image Upload (Style Transfer) -->
                            <div id="content-upload-section" class="mb-3">
                                <label class="form-label">Upload Content Image</label>
                                <input type="file" class="form-control" id="content-image" accept="image/*">
                                <div class="mt-2">
                                    <img id="content-preview" class="img-preview d-none" alt="Content preview">
                                </div>
                            </div>

                            <!-- Style Selection -->
                            <div id="style-selection-section" class="mb-3">
                                <label class="form-label">Choose Style</label>
                                <div id="style-grid" class="style-grid">
                                    <!-- Styles will be loaded here -->
                                </div>
                            </div>

                            <!-- Parameters -->
                            <div class="mb-3">
                                <label class="form-label">Parameters</label>
                                <div class="row">
                                    <div class="col-md-6" id="style-weight-section">
                                        <label class="form-label">Style Weight</label>
                                        <input type="range" class="form-range" id="style-weight" min="100000" max="10000000" value="1000000">
                                        <small class="text-muted">Higher values = more stylized</small>
                                    </div>
                                    <div class="col-md-6" id="content-weight-section">
                                        <label class="form-label">Content Weight</label>
                                        <input type="range" class="form-range" id="content-weight" min="0.1" max="10" step="0.1" value="1">
                                        <small class="text-muted">Higher values = more content preserved</small>
                                    </div>
                                    <div class="col-md-6 d-none" id="gan-seed-section">
                                        <label class="form-label">Seed (optional)</label>
                                        <input type="number" class="form-control" id="gan-seed" placeholder="Random if empty">
                                        <small class="text-muted">Use same seed for reproducible results</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Artwork Details -->
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" id="artwork-title" placeholder="My Amazing Art">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" id="artwork-description" rows="3" placeholder="Describe your artwork..."></textarea>
                            </div>

                            <!-- Generate Button -->
                            <button class="btn btn-primary btn-lg w-100" id="generate-btn" onclick="generateArt()">
                                <i class="fas fa-magic"></i> Generate Art
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Preview/Result Panel -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Preview</h5>
                        </div>
                        <div class="card-body text-center">
                            <div id="generation-status" class="d-none">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Generating...</span>
                                </div>
                                <p class="mt-2">Generating your artwork...</p>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                                </div>
                            </div>
                            <div id="result-section" class="d-none">
                                <img id="result-image" class="img-fluid rounded" alt="Generated artwork">
                                <div class="mt-3">
                                    <button class="btn btn-success" onclick="downloadArt()">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                    <button class="btn btn-info" onclick="shareArt()">
                                        <i class="fas fa-share"></i> Share
                                    </button>
                                </div>
                            </div>
                            <div id="preview-placeholder" class="text-muted">
                                <i class="fas fa-image fa-3x mb-3"></i>
                                <p>Your generated artwork will appear here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Section -->
        <div id="gallery-section" class="section d-none">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-images"></i> Gallery</h2>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary" onclick="loadGallery('created_at')">Recent</button>
                    <button class="btn btn-outline-secondary" onclick="loadGallery('likes')">Most Liked</button>
                    <button class="btn btn-outline-secondary" onclick="loadGallery('views')">Most Viewed</button>
                </div>
            </div>
            <div id="gallery-grid" class="row">
                <!-- Gallery items will be loaded here -->
            </div>
            <div class="text-center mt-4">
                <button class="btn btn-outline-primary" id="load-more-btn" onclick="loadMoreArtworks()">
                    Load More
                </button>
            </div>
        </div>

        <!-- Trending Section -->
        <div id="trending-section" class="section d-none">
            <h2><i class="fas fa-fire"></i> Trending</h2>
            <div id="trending-grid" class="row">
                <!-- Trending items will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Auth Modal -->
    <div class="modal fade" id="authModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Login / Register</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Username</label>
                        <input type="text" class="form-control" id="auth-username">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" id="auth-email">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Bio (optional)</label>
                        <textarea class="form-control" id="auth-bio" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="loginUser()">Login</button>
                    <button type="button" class="btn btn-primary" onclick="registerUser()">Register</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Artwork Detail Modal -->
    <div class="modal fade" id="artworkModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="artwork-modal-title"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <img id="artwork-modal-image" class="img-fluid rounded" alt="Artwork">
                        </div>
                        <div class="col-md-4">
                            <div id="artwork-modal-details">
                                <!-- Artwork details will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>

/* Custom styles for Generative Art Platform */

:root {
    --primary-color: #6f42c1;
    --secondary-color: #fd7e14;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #212529;
    --light-color: #f8f9fa;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    color: var(--secondary-color);
}

/* Section Management */
.section {
    display: none;
}

.section.active {
    display: block;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Style Grid */
.style-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: white;
}

.style-item {
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 0.375rem;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.style-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.style-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.25);
}

.style-item img {
    width: 100%;
    height: 80px;
    object-fit: cover;
}

.style-item .style-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 0.5rem 0.25rem 0.25rem;
    font-size: 0.75rem;
    text-align: center;
}

/* Image Previews */
.img-preview {
    max-width: 100%;
    max-height: 200px;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

/* Gallery Grid */
.gallery-item {
    margin-bottom: 2rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-item .card {
    height: 100%;
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-item .card-body {
    padding: 1rem;
}

.gallery-item .artwork-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.gallery-item .artwork-meta {
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gallery-item .artwork-stats {
    display: flex;
    gap: 1rem;
}

.gallery-item .artwork-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Generation Status */
#generation-status {
    padding: 2rem;
}

#generation-status .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Result Section */
#result-section img {
    max-height: 400px;
    border: 1px solid #dee2e6;
}

/* Preview Placeholder */
#preview-placeholder {
    padding: 3rem 1rem;
}

#preview-placeholder i {
    opacity: 0.5;
}

/* Form Controls */
.form-range {
    margin-bottom: 0.5rem;
}

.btn-group .btn-check:checked + .btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Generation Type Sections */
.generation-section {
    display: none;
}

.generation-section.active {
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .style-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem;
    }
    
    .style-item img {
        height: 60px;
    }
    
    .gallery-item img {
        height: 150px;
    }
    
    .card-body {
        padding: 0.75rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Artwork Modal */
#artworkModal .modal-dialog {
    max-width: 900px;
}

#artworkModal img {
    max-height: 500px;
    width: 100%;
    object-fit: contain;
}

.artwork-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.artwork-actions .btn {
    flex: 1;
}

/* User Avatar */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* Trending Section */
.trending-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.5rem;
}

.trending-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(111, 66, 193, 0.1), rgba(253, 126, 20, 0.1));
    z-index: 1;
}

.trending-item .card {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, var(--primary-color), var(--secondary-color)) border-box;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) 1;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .form-control {
        background-color: #3d3d3d;
        border-color: #555;
        color: #ffffff;
    }
    
    .form-control:focus {
        background-color: #3d3d3d;
        border-color: var(--primary-color);
        color: #ffffff;
    }
}

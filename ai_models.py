import torch
import torch.nn as nn
import torchvision.transforms as transforms
import torchvision.models as models
from PIL import Image
import numpy as np
import cv2
import os
import logging

logger = logging.getLogger(__name__)

class StyleTransferModel:
    """Neural Style Transfer using pre-trained VGG19"""
    
    def __init__(self, device='cpu'):
        self.device = device
        self.model = None
        self.load_model()
        
    def load_model(self):
        """Load pre-trained VGG19 model"""
        try:
            # Use VGG19 features for style transfer
            vgg = models.vgg19(pretrained=True).features.to(self.device).eval()
            
            # Freeze parameters
            for param in vgg.parameters():
                param.requires_grad_(False)
                
            self.model = vgg
            logger.info("VGG19 model loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading VGG19 model: {e}")
            raise
    
    def preprocess_image(self, image_path, size=512):
        """Preprocess image for style transfer"""
        try:
            image = Image.open(image_path).convert('RGB')
            
            # Resize while maintaining aspect ratio
            image.thumbnail((size, size), Image.Resampling.LANCZOS)
            
            # Convert to tensor
            transform = transforms.Compose([
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            
            tensor = transform(image).unsqueeze(0).to(self.device)
            return tensor, image.size
            
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path}: {e}")
            raise
    
    def get_features(self, image, layers=None):
        """Extract features from specified layers"""
        if layers is None:
            layers = {
                '0': 'conv1_1',
                '5': 'conv2_1', 
                '10': 'conv3_1',
                '19': 'conv4_1',
                '21': 'conv4_2',  # Content layer
                '28': 'conv5_1'
            }
        
        features = {}
        x = image
        
        for name, layer in self.model._modules.items():
            x = layer(x)
            if name in layers:
                features[layers[name]] = x
                
        return features
    
    def gram_matrix(self, tensor):
        """Calculate Gram matrix for style representation"""
        _, d, h, w = tensor.size()
        tensor = tensor.view(d, h * w)
        gram = torch.mm(tensor, tensor.t())
        return gram
    
    def style_transfer(self, content_path, style_path, output_path, 
                      num_steps=300, style_weight=1000000, content_weight=1):
        """Perform neural style transfer"""
        try:
            logger.info(f"Starting style transfer: {content_path} + {style_path}")
            
            # Load and preprocess images
            content_img, content_size = self.preprocess_image(content_path)
            style_img, _ = self.preprocess_image(style_path)
            
            # Initialize target image with content image
            target = content_img.clone().requires_grad_(True).to(self.device)
            
            # Get features
            content_features = self.get_features(content_img)
            style_features = self.get_features(style_img)
            
            # Calculate style gram matrices
            style_grams = {layer: self.gram_matrix(style_features[layer]) 
                          for layer in style_features}
            
            # Optimizer
            optimizer = torch.optim.Adam([target], lr=0.003)
            
            # Style transfer loop
            for step in range(num_steps):
                target_features = self.get_features(target)
                
                # Content loss
                content_loss = torch.mean((target_features['conv4_2'] - 
                                         content_features['conv4_2']) ** 2)
                
                # Style loss
                style_loss = 0
                for layer in style_grams:
                    target_feature = target_features[layer]
                    target_gram = self.gram_matrix(target_feature)
                    style_gram = style_grams[layer]
                    layer_style_loss = torch.mean((target_gram - style_gram) ** 2)
                    style_loss += layer_style_loss / (target_feature.shape[1] ** 2)
                
                # Total loss
                total_loss = content_weight * content_loss + style_weight * style_loss
                
                # Optimize
                optimizer.zero_grad()
                total_loss.backward()
                optimizer.step()
                
                if step % 50 == 0:
                    logger.info(f"Step {step}, Total loss: {total_loss.item():.4f}")
            
            # Save result
            self.save_tensor_as_image(target, output_path, content_size)
            logger.info(f"Style transfer completed: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error in style transfer: {e}")
            return False
    
    def save_tensor_as_image(self, tensor, path, size):
        """Convert tensor back to image and save"""
        try:
            # Denormalize
            tensor = tensor.cpu().clone().detach()
            tensor = tensor.squeeze(0)
            
            # Denormalize
            mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
            std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
            tensor = tensor * std + mean
            tensor = torch.clamp(tensor, 0, 1)
            
            # Convert to PIL Image
            transform = transforms.ToPILImage()
            image = transform(tensor)
            
            # Resize to original content size
            image = image.resize(size, Image.Resampling.LANCZOS)
            
            # Save
            image.save(path, quality=95)
            
        except Exception as e:
            logger.error(f"Error saving tensor as image: {e}")
            raise

class SimpleGAN:
    """Simple GAN for generating abstract art patterns"""
    
    def __init__(self, device='cpu'):
        self.device = device
        self.generator = None
        self.load_model()
    
    def load_model(self):
        """Load or create simple generator model"""
        try:
            # Simple generator architecture
            self.generator = nn.Sequential(
                # Input: noise vector (100,)
                nn.Linear(100, 256),
                nn.ReLU(True),
                nn.Linear(256, 512),
                nn.ReLU(True),
                nn.Linear(512, 1024),
                nn.ReLU(True),
                nn.Linear(1024, 3 * 256 * 256),  # RGB 256x256
                nn.Tanh()
            ).to(self.device)
            
            # Initialize weights
            self.generator.apply(self._init_weights)
            
            logger.info("Simple GAN generator loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading GAN model: {e}")
            raise
    
    def _init_weights(self, m):
        """Initialize model weights"""
        if isinstance(m, nn.Linear):
            torch.nn.init.normal_(m.weight, 0.0, 0.02)
            torch.nn.init.constant_(m.bias, 0)
    
    def generate_art(self, output_path, seed=None):
        """Generate abstract art using GAN"""
        try:
            if seed is not None:
                torch.manual_seed(seed)
            
            # Generate noise
            noise = torch.randn(1, 100).to(self.device)
            
            # Generate image
            with torch.no_grad():
                generated = self.generator(noise)
                
            # Reshape and process
            generated = generated.view(1, 3, 256, 256)
            generated = (generated + 1) / 2  # Scale from [-1,1] to [0,1]
            generated = torch.clamp(generated, 0, 1)
            
            # Convert to PIL and save
            transform = transforms.ToPILImage()
            image = transform(generated.squeeze(0).cpu())
            image.save(output_path, quality=95)
            
            logger.info(f"GAN art generated: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating GAN art: {e}")
            return False

# Global model instances
style_transfer_model = None
gan_model = None

def initialize_models():
    """Initialize AI models"""
    global style_transfer_model, gan_model
    
    try:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        logger.info(f"Using device: {device}")
        
        style_transfer_model = StyleTransferModel(device)
        gan_model = SimpleGAN(device)
        
        logger.info("All AI models initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing models: {e}")
        raise

def get_style_transfer_model():
    """Get style transfer model instance"""
    global style_transfer_model
    if style_transfer_model is None:
        initialize_models()
    return style_transfer_model

def get_gan_model():
    """Get GAN model instance"""
    global gan_model
    if gan_model is None:
        initialize_models()
    return gan_model

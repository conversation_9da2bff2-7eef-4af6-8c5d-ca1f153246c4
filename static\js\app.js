// Generative Art Platform - Frontend JavaScript

// Global variables
let currentUser = null;
let selectedStyle = null;
let currentArtworkId = null;
let galleryPage = 1;
let gallerySort = 'created_at';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Check authentication status
    checkAuthStatus();
    
    // Load styles
    loadStyles();
    
    // Set up event listeners
    setupEventListeners();
    
    // Load initial gallery
    loadGallery();
}

function setupEventListeners() {
    // Generation type change
    document.querySelectorAll('input[name="generation_type"]').forEach(radio => {
        radio.addEventListener('change', handleGenerationTypeChange);
    });
    
    // File upload preview
    document.getElementById('content-image').addEventListener('change', handleFileUpload);
    
    // Navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', handleNavigation);
    });
    
    // Parameter updates
    document.getElementById('style-weight').addEventListener('input', updateParameterDisplay);
    document.getElementById('content-weight').addEventListener('input', updateParameterDisplay);
}

function handleGenerationTypeChange(event) {
    const generationType = event.target.value;
    
    // Show/hide relevant sections
    const contentUpload = document.getElementById('content-upload-section');
    const styleSelection = document.getElementById('style-selection-section');
    const styleWeightSection = document.getElementById('style-weight-section');
    const contentWeightSection = document.getElementById('content-weight-section');
    const ganSeedSection = document.getElementById('gan-seed-section');
    
    if (generationType === 'style_transfer') {
        contentUpload.style.display = 'block';
        styleSelection.style.display = 'block';
        styleWeightSection.style.display = 'block';
        contentWeightSection.style.display = 'block';
        ganSeedSection.classList.add('d-none');
    } else if (generationType === 'gan') {
        contentUpload.style.display = 'none';
        styleSelection.style.display = 'none';
        styleWeightSection.style.display = 'none';
        contentWeightSection.style.display = 'none';
        ganSeedSection.classList.remove('d-none');
    }
}

function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('content-preview');
            preview.src = e.target.result;
            preview.classList.remove('d-none');
        };
        reader.readAsDataURL(file);
    }
}

function handleNavigation(event) {
    event.preventDefault();
    const href = event.target.getAttribute('href');
    
    if (href.startsWith('#')) {
        const sectionName = href.substring(1);
        showSection(sectionName);
    }
}

function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
        section.classList.add('d-none');
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.add('active');
        targetSection.classList.remove('d-none');
    }
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    const activeLink = document.querySelector(`a[href="#${sectionName}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // Load section-specific data
    if (sectionName === 'gallery') {
        loadGallery();
    } else if (sectionName === 'trending') {
        loadTrending();
    }
}

// Authentication functions
async function checkAuthStatus() {
    try {
        const response = await fetch('/auth/me');
        if (response.ok) {
            const data = await response.json();
            currentUser = data.user;
            updateAuthUI();
        }
    } catch (error) {
        console.error('Error checking auth status:', error);
    }
}

function updateAuthUI() {
    const authSection = document.getElementById('auth-section');
    const userMenu = document.getElementById('user-menu');
    const usernameDisplay = document.getElementById('username-display');
    
    if (currentUser) {
        authSection.classList.add('d-none');
        userMenu.classList.remove('d-none');
        usernameDisplay.textContent = currentUser.username;
    } else {
        authSection.classList.remove('d-none');
        userMenu.classList.add('d-none');
    }
}

function showAuthModal() {
    const modal = new bootstrap.Modal(document.getElementById('authModal'));
    modal.show();
}

async function loginUser() {
    const username = document.getElementById('auth-username').value;
    
    if (!username) {
        alert('Please enter a username');
        return;
    }
    
    try {
        const response = await fetch('/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentUser = data.user;
            updateAuthUI();
            bootstrap.Modal.getInstance(document.getElementById('authModal')).hide();
            showNotification('Login successful!', 'success');
        } else {
            showNotification(data.error || 'Login failed', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification('Login failed', 'error');
    }
}

async function registerUser() {
    const username = document.getElementById('auth-username').value;
    const email = document.getElementById('auth-email').value;
    const bio = document.getElementById('auth-bio').value;
    
    if (!username || !email) {
        alert('Please enter username and email');
        return;
    }
    
    try {
        const response = await fetch('/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, email, bio })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentUser = data.user;
            updateAuthUI();
            bootstrap.Modal.getInstance(document.getElementById('authModal')).hide();
            showNotification('Registration successful!', 'success');
        } else {
            showNotification(data.error || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showNotification('Registration failed', 'error');
    }
}

async function logout() {
    try {
        await fetch('/auth/logout', { method: 'POST' });
        currentUser = null;
        updateAuthUI();
        showNotification('Logged out successfully', 'success');
    } catch (error) {
        console.error('Logout error:', error);
    }
}

// Style loading and selection
async function loadStyles() {
    try {
        const response = await fetch('/api/styles');
        const data = await response.json();
        
        if (response.ok) {
            displayStyles(data.styles);
        }
    } catch (error) {
        console.error('Error loading styles:', error);
    }
}

function displayStyles(styles) {
    const styleGrid = document.getElementById('style-grid');
    styleGrid.innerHTML = '';
    
    styles.forEach(style => {
        const styleItem = document.createElement('div');
        styleItem.className = 'style-item';
        styleItem.onclick = () => selectStyle(style);
        
        styleItem.innerHTML = `
            <img src="/api/image/${style.thumbnail_path || style.style_image_path}" alt="${style.name}">
            <div class="style-name">${style.name}</div>
        `;
        
        styleGrid.appendChild(styleItem);
    });
}

function selectStyle(style) {
    selectedStyle = style;
    
    // Update UI
    document.querySelectorAll('.style-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    event.target.closest('.style-item').classList.add('selected');
}

// Art generation
async function generateArt() {
    if (!currentUser) {
        showAuthModal();
        return;
    }
    
    const generationType = document.querySelector('input[name="generation_type"]:checked').value;
    const title = document.getElementById('artwork-title').value || 'Untitled Artwork';
    const description = document.getElementById('artwork-description').value;
    
    // Validation
    if (generationType === 'style_transfer') {
        const contentFile = document.getElementById('content-image').files[0];
        if (!contentFile) {
            showNotification('Please upload a content image', 'error');
            return;
        }
        if (!selectedStyle) {
            showNotification('Please select a style', 'error');
            return;
        }
    }
    
    try {
        // Show loading state
        showGenerationStatus();
        
        let requestData = {
            generation_type: generationType,
            title,
            description,
            parameters: {}
        };
        
        if (generationType === 'style_transfer') {
            // Upload content image first
            const contentFile = document.getElementById('content-image').files[0];
            const uploadResult = await uploadFile(contentFile);
            
            if (!uploadResult.success) {
                throw new Error('Failed to upload content image');
            }
            
            requestData.content_image = uploadResult.filepath;
            requestData.style_id = selectedStyle.id;
            requestData.parameters = {
                style_weight: parseInt(document.getElementById('style-weight').value),
                content_weight: parseFloat(document.getElementById('content-weight').value),
                steps: 300
            };
        } else if (generationType === 'gan') {
            const seed = document.getElementById('gan-seed').value;
            if (seed) {
                requestData.parameters.seed = parseInt(seed);
            }
        }
        
        // Start generation
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });
        
        const data = await response.json();
        
        if (response.ok) {
            currentArtworkId = data.artwork_id;
            pollArtworkStatus(data.artwork_id);
        } else {
            throw new Error(data.error || 'Generation failed');
        }
        
    } catch (error) {
        console.error('Generation error:', error);
        showNotification(error.message || 'Generation failed', 'error');
        hideGenerationStatus();
    }
}

async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (response.ok) {
            return { success: true, ...data };
        } else {
            return { success: false, error: data.error };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function pollArtworkStatus(artworkId) {
    try {
        const response = await fetch(`/api/artwork/${artworkId}/status`);
        const data = await response.json();

        if (response.ok) {
            if (data.status === 'completed') {
                await loadArtworkResult(artworkId);
            } else if (data.status === 'failed') {
                showNotification('Artwork generation failed', 'error');
                hideGenerationStatus();
            } else {
                // Still processing, poll again
                setTimeout(() => pollArtworkStatus(artworkId), 2000);
            }
        }
    } catch (error) {
        console.error('Error polling artwork status:', error);
        setTimeout(() => pollArtworkStatus(artworkId), 5000);
    }
}

async function loadArtworkResult(artworkId) {
    try {
        const response = await fetch(`/api/artwork/${artworkId}`);
        const data = await response.json();

        if (response.ok) {
            displayArtworkResult(data.artwork);
            hideGenerationStatus();
        }
    } catch (error) {
        console.error('Error loading artwork result:', error);
        hideGenerationStatus();
    }
}

function displayArtworkResult(artwork) {
    const resultSection = document.getElementById('result-section');
    const resultImage = document.getElementById('result-image');
    const placeholder = document.getElementById('preview-placeholder');

    resultImage.src = `/api/image/${artwork.generated_image_path.split('/').pop()}`;
    resultSection.classList.remove('d-none');
    placeholder.classList.add('d-none');

    // Store current artwork for download/share
    window.currentArtwork = artwork;
}

function showGenerationStatus() {
    document.getElementById('generation-status').classList.remove('d-none');
    document.getElementById('result-section').classList.add('d-none');
    document.getElementById('preview-placeholder').classList.add('d-none');
    document.getElementById('generate-btn').disabled = true;
}

function hideGenerationStatus() {
    document.getElementById('generation-status').classList.add('d-none');
    document.getElementById('preview-placeholder').classList.remove('d-none');
    document.getElementById('generate-btn').disabled = false;
}

// Gallery functions
async function loadGallery(sortBy = 'created_at') {
    gallerySort = sortBy;
    galleryPage = 1;

    try {
        const response = await fetch(`/gallery/artworks?sort_by=${sortBy}&page=${galleryPage}`);
        const data = await response.json();

        if (response.ok) {
            displayGalleryArtworks(data.artworks, true);
            updateLoadMoreButton(data.pagination);
        }
    } catch (error) {
        console.error('Error loading gallery:', error);
    }
}

async function loadMoreArtworks() {
    galleryPage++;

    try {
        const response = await fetch(`/gallery/artworks?sort_by=${gallerySort}&page=${galleryPage}`);
        const data = await response.json();

        if (response.ok) {
            displayGalleryArtworks(data.artworks, false);
            updateLoadMoreButton(data.pagination);
        }
    } catch (error) {
        console.error('Error loading more artworks:', error);
        galleryPage--; // Revert page increment
    }
}

function displayGalleryArtworks(artworks, replace = true) {
    const galleryGrid = document.getElementById('gallery-grid');

    if (replace) {
        galleryGrid.innerHTML = '';
    }

    artworks.forEach(artwork => {
        const artworkElement = createArtworkElement(artwork);
        galleryGrid.appendChild(artworkElement);
    });
}

function createArtworkElement(artwork) {
    const col = document.createElement('div');
    col.className = 'col-md-4 col-sm-6 gallery-item fade-in';
    col.onclick = () => showArtworkModal(artwork.id);

    col.innerHTML = `
        <div class="card">
            <img src="/api/image/${artwork.generated_image_path.split('/').pop()}"
                 alt="${artwork.title}" class="card-img-top">
            <div class="card-body">
                <h6 class="artwork-title">${artwork.title}</h6>
                <div class="artwork-meta">
                    <small>by ${artwork.creator.username}</small>
                    <div class="artwork-stats">
                        <span><i class="fas fa-heart"></i> ${artwork.like_count}</span>
                        <span><i class="fas fa-eye"></i> ${artwork.view_count}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    return col;
}

function updateLoadMoreButton(pagination) {
    const loadMoreBtn = document.getElementById('load-more-btn');

    if (pagination.has_next) {
        loadMoreBtn.style.display = 'block';
    } else {
        loadMoreBtn.style.display = 'none';
    }
}

// Trending artworks
async function loadTrending() {
    try {
        const response = await fetch('/gallery/trending?limit=12');
        const data = await response.json();

        if (response.ok) {
            displayTrendingArtworks(data.trending_artworks);
        }
    } catch (error) {
        console.error('Error loading trending artworks:', error);
    }
}

function displayTrendingArtworks(artworks) {
    const trendingGrid = document.getElementById('trending-grid');
    trendingGrid.innerHTML = '';

    artworks.forEach(artwork => {
        const artworkElement = createArtworkElement(artwork);
        artworkElement.classList.add('trending-item');
        trendingGrid.appendChild(artworkElement);
    });
}

// Artwork modal
async function showArtworkModal(artworkId) {
    try {
        const response = await fetch(`/api/artwork/${artworkId}`);
        const data = await response.json();

        if (response.ok) {
            displayArtworkModal(data.artwork);
        }
    } catch (error) {
        console.error('Error loading artwork details:', error);
    }
}

function displayArtworkModal(artwork) {
    const modal = document.getElementById('artworkModal');
    const title = document.getElementById('artwork-modal-title');
    const image = document.getElementById('artwork-modal-image');
    const details = document.getElementById('artwork-modal-details');

    title.textContent = artwork.title;
    image.src = `/api/image/${artwork.generated_image_path.split('/').pop()}`;

    details.innerHTML = `
        <h6>Description</h6>
        <p>${artwork.description || 'No description provided'}</p>

        <h6>Details</h6>
        <ul class="list-unstyled">
            <li><strong>Artist:</strong> ${artwork.creator.username}</li>
            <li><strong>Type:</strong> ${artwork.generation_type.replace('_', ' ')}</li>
            <li><strong>Created:</strong> ${new Date(artwork.created_at).toLocaleDateString()}</li>
            <li><strong>Likes:</strong> ${artwork.like_count}</li>
            <li><strong>Views:</strong> ${artwork.view_count}</li>
        </ul>

        <div class="artwork-actions">
            <button class="btn btn-outline-primary" onclick="likeArtwork('${artwork.id}')">
                <i class="fas fa-heart"></i> Like
            </button>
            <button class="btn btn-outline-secondary" onclick="remixArtwork('${artwork.id}')">
                <i class="fas fa-remix"></i> Remix
            </button>
        </div>
    `;

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

// Social features
async function likeArtwork(artworkId) {
    if (!currentUser) {
        showAuthModal();
        return;
    }

    try {
        const response = await fetch(`/api/artwork/${artworkId}/like`, {
            method: 'POST'
        });

        const data = await response.json();

        if (response.ok) {
            showNotification(`Artwork ${data.action}!`, 'success');
            // Refresh gallery if visible
            if (!document.getElementById('gallery-section').classList.contains('d-none')) {
                loadGallery(gallerySort);
            }
        }
    } catch (error) {
        console.error('Error liking artwork:', error);
        showNotification('Failed to like artwork', 'error');
    }
}

// Utility functions
function updateParameterDisplay() {
    // Update parameter display values if needed
    const styleWeight = document.getElementById('style-weight').value;
    const contentWeight = document.getElementById('content-weight').value;

    // You can add visual feedback here
}

function downloadArt() {
    if (window.currentArtwork) {
        const link = document.createElement('a');
        link.href = `/api/image/${window.currentArtwork.generated_image_path.split('/').pop()}`;
        link.download = `${window.currentArtwork.title}.jpg`;
        link.click();
    }
}

function shareArt() {
    if (window.currentArtwork && navigator.share) {
        navigator.share({
            title: window.currentArtwork.title,
            text: `Check out this AI-generated artwork: ${window.currentArtwork.title}`,
            url: window.location.href
        });
    } else {
        // Fallback: copy link to clipboard
        navigator.clipboard.writeText(window.location.href);
        showNotification('Link copied to clipboard!', 'success');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

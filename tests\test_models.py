#!/usr/bin/env python3
"""
Test suite for database models
"""

import unittest
import sys
import os

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestModels(unittest.TestCase):
    """Test database models"""
    
    def setUp(self):
        """Set up test environment"""
        try:
            from app import app
            from models import db, User, Style, Artwork, Like, Comment
            
            self.app = app
            self.db = db
            self.User = User
            self.Style = Style
            self.Artwork = Artwork
            self.Like = Like
            self.Comment = Comment
            
            # Configure for testing
            app.config['TESTING'] = True
            app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
            
            with app.app_context():
                db.create_all()
                
        except ImportError as e:
            self.skipTest(f"Required modules not available: {e}")
    
    def test_user_model(self):
        """Test User model"""
        with self.app.app_context():
            user = self.User(
                username='testuser',
                email='<EMAIL>',
                bio='Test user bio'
            )
            
            self.db.session.add(user)
            self.db.session.commit()
            
            # Test user was created
            self.assertIsNotNone(user.id)
            self.assertEqual(user.username, 'testuser')
            self.assertEqual(user.email, '<EMAIL>')
            
            # Test to_dict method
            user_dict = user.to_dict()
            self.assertIn('id', user_dict)
            self.assertIn('username', user_dict)
            self.assertIn('email', user_dict)
    
    def test_style_model(self):
        """Test Style model"""
        with self.app.app_context():
            style = self.Style(
                name='Test Style',
                description='A test style',
                style_image_path='/path/to/style.jpg',
                category='test'
            )
            
            self.db.session.add(style)
            self.db.session.commit()
            
            # Test style was created
            self.assertIsNotNone(style.id)
            self.assertEqual(style.name, 'Test Style')
            self.assertTrue(style.is_active)
            
            # Test to_dict method
            style_dict = style.to_dict()
            self.assertIn('id', style_dict)
            self.assertIn('name', style_dict)
    
    def test_artwork_model(self):
        """Test Artwork model"""
        with self.app.app_context():
            # Create user and style first
            user = self.User(username='artist', email='<EMAIL>')
            style = self.Style(name='Art Style', style_image_path='/path/to/style.jpg')
            
            self.db.session.add(user)
            self.db.session.add(style)
            self.db.session.commit()
            
            # Create artwork
            artwork = self.Artwork(
                title='Test Artwork',
                description='A test artwork',
                original_image_path='/path/to/original.jpg',
                generated_image_path='/path/to/generated.jpg',
                user_id=user.id,
                style_id=style.id,
                generation_type='style_transfer'
            )
            
            self.db.session.add(artwork)
            self.db.session.commit()
            
            # Test artwork was created
            self.assertIsNotNone(artwork.id)
            self.assertEqual(artwork.title, 'Test Artwork')
            self.assertEqual(artwork.generation_type, 'style_transfer')
            self.assertEqual(artwork.processing_status, 'pending')
            
            # Test relationships
            self.assertEqual(artwork.creator.username, 'artist')
            self.assertEqual(artwork.style.name, 'Art Style')
    
    def test_like_model(self):
        """Test Like model"""
        with self.app.app_context():
            # Create user, style, and artwork
            user = self.User(username='liker', email='<EMAIL>')
            style = self.Style(name='Like Style', style_image_path='/path/to/style.jpg')
            
            self.db.session.add(user)
            self.db.session.add(style)
            self.db.session.commit()
            
            artwork = self.Artwork(
                title='Likeable Art',
                user_id=user.id,
                style_id=style.id,
                generation_type='gan',
                original_image_path='/path/to/original.jpg',
                generated_image_path='/path/to/generated.jpg'
            )
            
            self.db.session.add(artwork)
            self.db.session.commit()
            
            # Create like
            like = self.Like(user_id=user.id, artwork_id=artwork.id)
            self.db.session.add(like)
            self.db.session.commit()
            
            # Test like was created
            self.assertIsNotNone(like.id)
            self.assertEqual(like.user_id, user.id)
            self.assertEqual(like.artwork_id, artwork.id)
    
    def test_comment_model(self):
        """Test Comment model"""
        with self.app.app_context():
            # Create user, style, and artwork
            user = self.User(username='commenter', email='<EMAIL>')
            style = self.Style(name='Comment Style', style_image_path='/path/to/style.jpg')
            
            self.db.session.add(user)
            self.db.session.add(style)
            self.db.session.commit()
            
            artwork = self.Artwork(
                title='Commentable Art',
                user_id=user.id,
                style_id=style.id,
                generation_type='style_transfer',
                original_image_path='/path/to/original.jpg',
                generated_image_path='/path/to/generated.jpg'
            )
            
            self.db.session.add(artwork)
            self.db.session.commit()
            
            # Create comment
            comment = self.Comment(
                content='Great artwork!',
                user_id=user.id,
                artwork_id=artwork.id
            )
            
            self.db.session.add(comment)
            self.db.session.commit()
            
            # Test comment was created
            self.assertIsNotNone(comment.id)
            self.assertEqual(comment.content, 'Great artwork!')
            
            # Test to_dict method
            comment_dict = comment.to_dict()
            self.assertIn('id', comment_dict)
            self.assertIn('content', comment_dict)
            self.assertIn('author', comment_dict)

if __name__ == '__main__':
    unittest.main()

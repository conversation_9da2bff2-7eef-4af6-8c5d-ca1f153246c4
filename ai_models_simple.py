"""
Simplified AI models for testing without PyTorch dependencies
This is a fallback version that creates placeholder images
"""

from PIL import Image, ImageDraw, ImageFont
import os
import logging
import random

logger = logging.getLogger(__name__)

class SimpleStyleTransferModel:
    """Simple style transfer that creates a placeholder image"""
    
    def __init__(self, device='cpu'):
        self.device = device
        logger.info("Simple style transfer model initialized")
    
    def style_transfer(self, content_path, style_path, output_path, **kwargs):
        """Create a simple style transfer placeholder"""
        try:
            # Load content image
            content_img = Image.open(content_path)
            width, height = content_img.size
            
            # Create a new image with some style effects
            result = Image.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(result)
            
            # Apply some simple "style" effects
            # Add colored rectangles to simulate style transfer
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7']
            
            for i in range(20):
                x1 = random.randint(0, width//2)
                y1 = random.randint(0, height//2)
                x2 = x1 + random.randint(50, width//4)
                y2 = y1 + random.randint(50, height//4)
                color = random.choice(colors)
                draw.rectangle([x1, y1, x2, y2], fill=color, outline=None)
            
            # Blend with original content
            result = Image.blend(content_img.convert('RGB'), result, alpha=0.3)
            
            # Add text overlay
            try:
                font = ImageFont.load_default()
                draw = ImageDraw.Draw(result)
                draw.text((10, 10), "Style Transfer Demo", fill='white', font=font)
            except:
                pass
            
            # Save result
            result.save(output_path, 'JPEG', quality=95)
            logger.info(f"Simple style transfer completed: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error in simple style transfer: {e}")
            return False

class SimpleGAN:
    """Simple GAN that creates abstract art patterns"""
    
    def __init__(self, device='cpu'):
        self.device = device
        logger.info("Simple GAN model initialized")
    
    def generate_art(self, output_path, seed=None):
        """Generate simple abstract art"""
        try:
            if seed is not None:
                random.seed(seed)
            
            # Create abstract art
            width, height = 512, 512
            image = Image.new('RGB', (width, height), color='black')
            draw = ImageDraw.Draw(image)
            
            # Generate random abstract patterns
            colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', 
                     '#dda0dd', '#98fb98', '#f0e68c', '#deb887', '#ffa07a']
            
            # Draw random shapes
            for i in range(50):
                shape_type = random.choice(['circle', 'rectangle', 'line'])
                color = random.choice(colors)
                
                if shape_type == 'circle':
                    x = random.randint(0, width)
                    y = random.randint(0, height)
                    r = random.randint(10, 100)
                    draw.ellipse([x-r, y-r, x+r, y+r], fill=color)
                    
                elif shape_type == 'rectangle':
                    x1 = random.randint(0, width)
                    y1 = random.randint(0, height)
                    x2 = x1 + random.randint(20, 200)
                    y2 = y1 + random.randint(20, 200)
                    draw.rectangle([x1, y1, x2, y2], fill=color)
                    
                elif shape_type == 'line':
                    x1 = random.randint(0, width)
                    y1 = random.randint(0, height)
                    x2 = random.randint(0, width)
                    y2 = random.randint(0, height)
                    draw.line([x1, y1, x2, y2], fill=color, width=random.randint(1, 10))
            
            # Add text overlay
            try:
                font = ImageFont.load_default()
                draw.text((10, height-30), "Generated Art Demo", fill='white', font=font)
            except:
                pass
            
            # Save result
            image.save(output_path, 'JPEG', quality=95)
            logger.info(f"Simple GAN art generated: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating simple GAN art: {e}")
            return False

# Global model instances
style_transfer_model = None
gan_model = None

def initialize_models():
    """Initialize simple AI models"""
    global style_transfer_model, gan_model
    
    try:
        style_transfer_model = SimpleStyleTransferModel()
        gan_model = SimpleGAN()
        logger.info("Simple AI models initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing simple models: {e}")
        raise

def get_style_transfer_model():
    """Get style transfer model instance"""
    global style_transfer_model
    if style_transfer_model is None:
        initialize_models()
    return style_transfer_model

def get_gan_model():
    """Get GAN model instance"""
    global gan_model
    if gan_model is None:
        initialize_models()
    return gan_model

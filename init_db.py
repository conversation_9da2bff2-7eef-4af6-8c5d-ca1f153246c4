#!/usr/bin/env python3
"""
Database initialization script for Generative Art Platform
Creates tables and populates with sample data
"""

import os
import sys
from PIL import Image, ImageDraw
import numpy as np

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, User, Style, Artwork
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_style_image(name, color_scheme, size=(256, 256)):
    """Create a sample style image with given color scheme"""
    image = Image.new('RGB', size, color='white')
    draw = ImageDraw.Draw(image)
    
    # Create different patterns based on style name
    if 'abstract' in name.lower():
        # Abstract geometric patterns
        for i in range(20):
            x1, y1 = np.random.randint(0, size[0], 2)
            x2, y2 = np.random.randint(0, size[0], 2)
            color = np.random.choice(color_scheme)
            draw.rectangle([x1, y1, x2, y2], fill=color)
            
    elif 'impressionist' in name.lower():
        # Impressionist-style brush strokes
        for i in range(100):
            x, y = np.random.randint(0, size[0], 2)
            r = np.random.randint(5, 20)
            color = np.random.choice(color_scheme)
            draw.ellipse([x-r, y-r, x+r, y+r], fill=color)
            
    elif 'cubist' in name.lower():
        # Cubist geometric shapes
        for i in range(15):
            points = []
            for j in range(np.random.randint(3, 6)):
                points.append((np.random.randint(0, size[0]), np.random.randint(0, size[1])))
            color = np.random.choice(color_scheme)
            draw.polygon(points, fill=color)
            
    elif 'watercolor' in name.lower():
        # Watercolor-like blobs
        for i in range(30):
            x, y = np.random.randint(0, size[0], 2)
            r = np.random.randint(10, 40)
            color = np.random.choice(color_scheme)
            draw.ellipse([x-r, y-r, x+r, y+r], fill=color)
            
    else:
        # Default pattern - stripes
        stripe_width = size[1] // len(color_scheme)
        for i, color in enumerate(color_scheme):
            y1 = i * stripe_width
            y2 = (i + 1) * stripe_width
            draw.rectangle([0, y1, size[0], y2], fill=color)
    
    return image

def create_sample_styles():
    """Create sample style images and database entries"""
    styles_data = [
        {
            'name': 'Van Gogh Starry Night',
            'description': 'Swirling, dynamic brushstrokes inspired by Van Gogh',
            'category': 'classical',
            'colors': ['#1e3a8a', '#fbbf24', '#f59e0b', '#374151']
        },
        {
            'name': 'Picasso Cubist',
            'description': 'Geometric, fragmented style of Picasso',
            'category': 'modern',
            'colors': ['#dc2626', '#059669', '#7c3aed', '#f59e0b']
        },
        {
            'name': 'Monet Impressionist',
            'description': 'Soft, light-filled impressionist style',
            'category': 'classical',
            'colors': ['#bfdbfe', '#fecaca', '#fed7aa', '#d1fae5']
        },
        {
            'name': 'Abstract Expressionist',
            'description': 'Bold, expressive abstract forms',
            'category': 'abstract',
            'colors': ['#ef4444', '#3b82f6', '#eab308', '#22c55e']
        },
        {
            'name': 'Japanese Ukiyo-e',
            'description': 'Traditional Japanese woodblock print style',
            'category': 'traditional',
            'colors': ['#1f2937', '#dc2626', '#f59e0b', '#e5e7eb']
        },
        {
            'name': 'Watercolor Dreams',
            'description': 'Soft, flowing watercolor effects',
            'category': 'modern',
            'colors': ['#a7f3d0', '#fde68a', '#fbb6ce', '#c7d2fe']
        },
        {
            'name': 'Pop Art Bold',
            'description': 'Bright, bold pop art style',
            'category': 'modern',
            'colors': ['#ff0080', '#00ff80', '#8000ff', '#ffff00']
        },
        {
            'name': 'Renaissance Classic',
            'description': 'Classical Renaissance painting style',
            'category': 'classical',
            'colors': ['#92400e', '#7c2d12', '#fbbf24', '#e5e7eb']
        }
    ]
    
    os.makedirs('static/styles', exist_ok=True)
    
    for style_data in styles_data:
        # Create style image
        image = create_sample_style_image(style_data['name'], style_data['colors'])
        
        # Save style image
        filename = style_data['name'].lower().replace(' ', '_') + '.jpg'
        filepath = os.path.join('static/styles', filename)
        image.save(filepath, 'JPEG', quality=95)
        
        # Create thumbnail
        thumbnail = image.copy()
        thumbnail.thumbnail((128, 128), Image.Resampling.LANCZOS)
        thumb_filename = style_data['name'].lower().replace(' ', '_') + '_thumb.jpg'
        thumb_filepath = os.path.join('static/styles', thumb_filename)
        thumbnail.save(thumb_filepath, 'JPEG', quality=95)
        
        # Create database entry
        style = Style(
            name=style_data['name'],
            description=style_data['description'],
            style_image_path=filepath,
            thumbnail_path=thumb_filepath,
            category=style_data['category']
        )
        
        db.session.add(style)
        logger.info(f"Created style: {style_data['name']}")
    
    db.session.commit()
    logger.info("Sample styles created successfully")

def create_sample_users():
    """Create sample users"""
    users_data = [
        {
            'username': 'artist_demo',
            'email': '<EMAIL>',
            'bio': 'Demo artist account for testing the platform'
        },
        {
            'username': 'creative_soul',
            'email': '<EMAIL>',
            'bio': 'Passionate about AI-generated art and digital creativity'
        }
    ]
    
    for user_data in users_data:
        # Check if user already exists
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if not existing_user:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                bio=user_data['bio']
            )
            db.session.add(user)
            logger.info(f"Created user: {user_data['username']}")
    
    db.session.commit()
    logger.info("Sample users created successfully")

def initialize_database():
    """Initialize the database with tables and sample data"""
    try:
        with app.app_context():
            logger.info("Creating database tables...")
            db.create_all()
            
            logger.info("Creating sample styles...")
            create_sample_styles()
            
            logger.info("Creating sample users...")
            create_sample_users()
            
            logger.info("Database initialization completed successfully!")
            
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise

if __name__ == '__main__':
    initialize_database()

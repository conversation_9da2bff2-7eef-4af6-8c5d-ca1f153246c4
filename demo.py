#!/usr/bin/env python3
"""
Demo script for Generative Art Platform
Shows the platform functionality without requiring full ML dependencies
"""

import os
import sys
import subprocess
import time

def check_dependencies():
    """Check if basic dependencies are available"""
    try:
        import flask
        import flask_sqlalchemy
        import PIL
        print("✓ Basic dependencies are available")
        return True
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        print("Please install dependencies first:")
        print("pip install -r requirements_minimal.txt")
        return False

def setup_demo():
    """Set up demo environment"""
    print("Setting up demo environment...")
    
    # Create directories
    directories = ['uploads', 'generated', 'static/styles']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")
    
    # Initialize database
    try:
        print("Initializing database...")
        subprocess.run([sys.executable, 'init_db_simple.py'], check=True)
        print("✓ Database initialized")
    except subprocess.CalledProcessError:
        print("✗ Failed to initialize database")
        return False
    
    return True

def create_sample_content():
    """Create sample content image for demo"""
    try:
        from PIL import Image, ImageDraw
        
        # Create a simple sample image
        img = Image.new('RGB', (300, 300), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        # Draw a simple house
        draw.rectangle([100, 150, 200, 250], fill='brown')  # House
        draw.polygon([(80, 150), (150, 100), (220, 150)], fill='red')  # Roof
        draw.rectangle([130, 200, 170, 250], fill='darkblue')  # Door
        draw.rectangle([110, 170, 140, 200], fill='yellow')  # Window 1
        draw.rectangle([160, 170, 190, 200], fill='yellow')  # Window 2
        
        # Save sample image
        sample_path = os.path.join('uploads', 'sample_house.jpg')
        img.save(sample_path, 'JPEG')
        print(f"✓ Created sample content image: {sample_path}")
        return sample_path
        
    except Exception as e:
        print(f"✗ Failed to create sample content: {e}")
        return None

def run_demo():
    """Run the demo"""
    print("=" * 60)
    print("GENERATIVE ART PLATFORM - DEMO")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        return False
    
    # Setup demo
    if not setup_demo():
        return False
    
    # Create sample content
    sample_image = create_sample_content()
    
    print("\n" + "=" * 60)
    print("DEMO SETUP COMPLETE!")
    print("=" * 60)
    
    print("\nTo run the application:")
    print("1. python app.py")
    print("2. Open http://localhost:5000 in your browser")
    
    print("\nDemo features available:")
    print("• User registration and login")
    print("• Style selection from predefined styles")
    print("• Image upload and processing")
    print("• Simple AI art generation (demo mode)")
    print("• Gallery viewing and social features")
    
    if sample_image:
        print(f"\nSample content image created: {sample_image}")
        print("You can use this for testing style transfer!")
    
    print("\nStarting the application...")
    
    # Try to start the application
    try:
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\nDemo stopped by user")
    except Exception as e:
        print(f"Error running application: {e}")
    
    return True

def show_help():
    """Show help information"""
    print("Generative Art Platform Demo")
    print("Usage: python demo.py [command]")
    print()
    print("Commands:")
    print("  run     - Run the full demo (default)")
    print("  setup   - Just setup the environment")
    print("  test    - Run basic tests")
    print("  help    - Show this help")

def run_tests():
    """Run basic tests"""
    try:
        subprocess.run([sys.executable, 'test_basic.py'], check=True)
    except subprocess.CalledProcessError:
        print("Tests failed")
        return False
    return True

def main():
    """Main demo function"""
    command = sys.argv[1] if len(sys.argv) > 1 else 'run'
    
    if command == 'help':
        show_help()
    elif command == 'setup':
        setup_demo()
    elif command == 'test':
        run_tests()
    elif command == 'run':
        run_demo()
    else:
        print(f"Unknown command: {command}")
        show_help()

if __name__ == '__main__':
    main()

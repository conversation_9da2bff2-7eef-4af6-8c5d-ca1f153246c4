#!/usr/bin/env python3
"""
Basic test to verify the application structure works
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test models import
        from models import db, User, Style, Artwork, Like, Comment
        print("✓ Models imported successfully")
        
        # Test app import
        from app import app
        print("✓ App imported successfully")
        
        print("✓ All basic imports successful!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_app_config():
    """Test app configuration"""
    try:
        from app import app
        
        print("Testing app configuration...")
        
        # Check basic config
        assert app.config['SQLALCHEMY_DATABASE_URI'] is not None
        assert app.config['SECRET_KEY'] is not None
        assert app.config['UPLOAD_FOLDER'] == 'uploads'
        assert app.config['GENERATED_FOLDER'] == 'generated'
        
        print("✓ App configuration is valid")
        return True
        
    except Exception as e:
        print(f"✗ App configuration error: {e}")
        return False

def test_database_models():
    """Test database model definitions"""
    try:
        from models import User, Style, Artwork, Like, Comment
        
        print("Testing database models...")
        
        # Check model attributes exist
        assert hasattr(User, 'id')
        assert hasattr(User, 'username')
        assert hasattr(User, 'email')
        
        assert hasattr(Style, 'id')
        assert hasattr(Style, 'name')
        assert hasattr(Style, 'style_image_path')
        
        assert hasattr(Artwork, 'id')
        assert hasattr(Artwork, 'title')
        assert hasattr(Artwork, 'user_id')
        
        print("✓ Database models are properly defined")
        return True
        
    except Exception as e:
        print(f"✗ Database model error: {e}")
        return False

def test_routes_structure():
    """Test that route modules exist and have basic structure"""
    try:
        print("Testing route structure...")
        
        # Check route files exist
        assert os.path.exists('routes/__init__.py')
        assert os.path.exists('routes/auth.py')
        assert os.path.exists('routes/api.py')
        assert os.path.exists('routes/gallery.py')
        
        print("✓ Route files exist")
        return True
        
    except Exception as e:
        print(f"✗ Route structure error: {e}")
        return False

def test_static_files():
    """Test that static files exist"""
    try:
        print("Testing static files...")
        
        # Check static directories and files exist
        assert os.path.exists('static/css/style.css')
        assert os.path.exists('static/js/app.js')
        assert os.path.exists('templates/index.html')
        
        print("✓ Static files exist")
        return True
        
    except Exception as e:
        print(f"✗ Static files error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("GENERATIVE ART PLATFORM - BASIC TESTS")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_app_config,
        test_database_models,
        test_routes_structure,
        test_static_files
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        else:
            print("Test failed!")
    
    print()
    print("=" * 50)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Basic structure is working.")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Initialize database: python init_db.py")
        print("3. Run application: python app.py")
    else:
        print("✗ Some tests failed. Please fix the issues above.")
    
    print("=" * 50)
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

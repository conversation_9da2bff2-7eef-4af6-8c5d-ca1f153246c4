# Generative Art Platform

A web service that lets users create AI-generated art using style transfer and simple GAN models. Users can upload content images, choose style sources, tweak parameters, and export or gallery-share their creations with social features for remixing and liking art.

## Features

- **AI Art Generation**: Style transfer using neural networks and GAN-based art generation
- **Interactive Web Interface**: Upload images, select styles, adjust parameters
- **Social Features**: Public gallery, likes, comments, remixing
- **User Profiles**: Personal galleries and artwork management
- **Real-time Processing**: Background processing with status updates
- **Export & Share**: Download generated artworks and share with others

## Technology Stack

- **Backend**: Python Flask with SQLAlchemy
- **AI/ML**: PyTorch for neural networks, PIL for image processing
- **Frontend**: HTML5, CSS3, JavaScript with Bootstrap 5
- **Database**: SQLite (development) / PostgreSQL (production)
- **File Storage**: Local filesystem with organized directory structure

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Virtual environment (recommended)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd generative-art-platform
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv venv
   
   # On Windows
   venv\Scripts\activate
   
   # On macOS/Linux
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Initialize the database**
   ```bash
   python init_db.py
   ```

6. **Run the application**
   ```bash
   python app.py
   ```

7. **Access the application**
   Open your browser and navigate to `http://localhost:5000`

## Usage

### Creating Art

1. **Register/Login**: Create an account or login with existing credentials
2. **Choose Generation Type**:
   - **Style Transfer**: Upload a content image and select a style
   - **GAN Art**: Generate abstract art from noise
3. **Adjust Parameters**: Fine-tune style weight, content weight, or other settings
4. **Generate**: Click generate and wait for processing to complete
5. **Download/Share**: Save your artwork or share it to the public gallery

### Gallery Features

- **Browse**: Explore public artworks by recent, most liked, or most viewed
- **Interact**: Like artworks and leave comments
- **Remix**: Create variations of existing artworks
- **Profile**: View user profiles and their artwork collections

## API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `POST /auth/logout` - Logout user
- `GET /auth/me` - Get current user info

### Art Generation
- `GET /api/styles` - Get available styles
- `POST /api/upload` - Upload content image
- `POST /api/generate` - Start artwork generation
- `GET /api/artwork/{id}/status` - Check generation status
- `GET /api/artwork/{id}` - Get artwork details

### Gallery
- `GET /gallery/artworks` - Get public artworks (paginated)
- `GET /gallery/trending` - Get trending artworks
- `GET /gallery/user/{id}/artworks` - Get user's artworks
- `POST /api/artwork/{id}/like` - Like/unlike artwork
- `POST /gallery/artwork/{id}/remix` - Create remix

## Configuration

### Environment Variables

- `SECRET_KEY`: Flask secret key for sessions
- `DATABASE_URL`: Database connection string
- `UPLOAD_FOLDER`: Directory for uploaded files
- `GENERATED_FOLDER`: Directory for generated artworks
- `MAX_CONTENT_LENGTH`: Maximum file upload size

### AI Model Configuration

The platform uses:
- **VGG19** for style transfer feature extraction
- **Simple GAN** for abstract art generation
- **PyTorch** as the ML framework

Models are loaded on-demand and cached for performance.

## Development

### Project Structure

```
generative-art-platform/
├── app.py                 # Main Flask application
├── models.py             # Database models
├── ai_models.py          # AI model implementations
├── init_db.py           # Database initialization
├── requirements.txt      # Python dependencies
├── routes/              # API route handlers
│   ├── auth.py         # Authentication routes
│   ├── api.py          # Core API endpoints
│   └── gallery.py      # Gallery and social features
├── templates/           # HTML templates
│   └── index.html      # Main application template
├── static/             # Static assets
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   ├── images/        # Static images
│   └── styles/        # Style reference images
├── uploads/            # User uploaded files
└── generated/          # Generated artworks
```

### Adding New Styles

1. Add style images to `static/styles/`
2. Create database entries in `init_db.py`
3. Restart the application

### Extending AI Models

1. Implement new model class in `ai_models.py`
2. Add generation logic to API routes
3. Update frontend interface as needed

## Testing

Run tests with pytest:
```bash
pytest tests/
```

## Deployment

### Production Setup

1. **Use PostgreSQL database**
   ```bash
   pip install psycopg2-binary
   export DATABASE_URL=postgresql://user:pass@localhost/artgen
   ```

2. **Use Gunicorn WSGI server**
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

3. **Set up reverse proxy** (nginx recommended)

4. **Configure environment variables** for production

### Docker Deployment

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review existing issues for solutions

## Roadmap

- [ ] Advanced GAN models (StyleGAN, CycleGAN)
- [ ] Video style transfer
- [ ] Mobile app
- [ ] Cloud storage integration
- [ ] Advanced social features
- [ ] Marketplace for selling artworks
- [ ] API rate limiting and authentication
- [ ] Advanced user roles and permissions

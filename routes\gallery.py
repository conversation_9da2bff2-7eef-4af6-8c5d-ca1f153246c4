from flask import Blueprint, request, jsonify, session
from models import User, Artwork, Style, Like, Comment, db
from sqlalchemy import desc, func
import logging

logger = logging.getLogger(__name__)

gallery_bp = Blueprint('gallery', __name__)

@gallery_bp.route('/artworks', methods=['GET'])
def get_public_artworks():
    """Get public artworks with pagination and filtering"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        sort_by = request.args.get('sort_by', 'created_at')  # created_at, likes, views
        category = request.args.get('category')
        user_id = request.args.get('user_id')
        
        # Base query for public artworks
        query = Artwork.query.filter_by(is_public=True, processing_status='completed')
        
        # Apply filters
        if category:
            query = query.join(Style).filter(Style.category == category)
            
        if user_id:
            query = query.filter_by(user_id=user_id)
        
        # Apply sorting
        if sort_by == 'likes':
            query = query.order_by(desc(Artwork.like_count))
        elif sort_by == 'views':
            query = query.order_by(desc(Artwork.view_count))
        else:  # created_at
            query = query.order_by(desc(Artwork.created_at))
        
        # Paginate
        pagination = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        artworks = [artwork.to_dict() for artwork in pagination.items]
        
        return jsonify({
            'artworks': artworks,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching public artworks: {e}")
        return jsonify({'error': 'Failed to fetch artworks'}), 500

@gallery_bp.route('/trending', methods=['GET'])
def get_trending_artworks():
    """Get trending artworks based on recent likes and views"""
    try:
        limit = min(request.args.get('limit', 10, type=int), 50)
        
        # Calculate trending score (likes + views with time decay)
        # Simple implementation: recent artworks with high engagement
        artworks = db.session.query(Artwork).filter_by(
            is_public=True, 
            processing_status='completed'
        ).order_by(
            desc(Artwork.like_count + Artwork.view_count)
        ).limit(limit).all()
        
        return jsonify({
            'trending_artworks': [artwork.to_dict() for artwork in artworks]
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching trending artworks: {e}")
        return jsonify({'error': 'Failed to fetch trending artworks'}), 500

@gallery_bp.route('/featured', methods=['GET'])
def get_featured_artworks():
    """Get featured artworks (high quality, diverse styles)"""
    try:
        limit = min(request.args.get('limit', 12, type=int), 50)
        
        # Simple featured algorithm: high likes, diverse styles
        featured = db.session.query(Artwork).filter_by(
            is_public=True,
            processing_status='completed'
        ).filter(
            Artwork.like_count >= 5  # Minimum likes threshold
        ).order_by(
            desc(Artwork.like_count)
        ).limit(limit).all()
        
        return jsonify({
            'featured_artworks': [artwork.to_dict() for artwork in featured]
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching featured artworks: {e}")
        return jsonify({'error': 'Failed to fetch featured artworks'}), 500

@gallery_bp.route('/user/<user_id>/artworks', methods=['GET'])
def get_user_artworks(user_id):
    """Get artworks by specific user"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get user's public artworks (or all if viewing own profile)
        current_user_id = session.get('user_id')
        if current_user_id == user_id:
            # User viewing own artworks - show all
            query = Artwork.query.filter_by(user_id=user_id)
        else:
            # Others viewing - show only public
            query = Artwork.query.filter_by(user_id=user_id, is_public=True)
        
        query = query.filter_by(processing_status='completed').order_by(desc(Artwork.created_at))
        
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        artworks = [artwork.to_dict() for artwork in pagination.items]
        
        return jsonify({
            'user': user.to_dict(),
            'artworks': artworks,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching user artworks: {e}")
        return jsonify({'error': 'Failed to fetch user artworks'}), 500

@gallery_bp.route('/artwork/<artwork_id>/comments', methods=['GET'])
def get_artwork_comments(artwork_id):
    """Get comments for an artwork"""
    try:
        artwork = Artwork.query.get(artwork_id)
        if not artwork:
            return jsonify({'error': 'Artwork not found'}), 404
        
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 20, type=int), 100)
        
        pagination = Comment.query.filter_by(artwork_id=artwork_id).order_by(
            desc(Comment.created_at)
        ).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        comments = [comment.to_dict() for comment in pagination.items]
        
        return jsonify({
            'comments': comments,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching comments: {e}")
        return jsonify({'error': 'Failed to fetch comments'}), 500

@gallery_bp.route('/artwork/<artwork_id>/comments', methods=['POST'])
def add_comment(artwork_id):
    """Add comment to artwork"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'Authentication required'}), 401
        
        artwork = Artwork.query.get(artwork_id)
        if not artwork:
            return jsonify({'error': 'Artwork not found'}), 404
        
        data = request.get_json()
        if not data or not data.get('content'):
            return jsonify({'error': 'Comment content is required'}), 400
        
        comment = Comment(
            content=data['content'].strip(),
            user_id=user_id,
            artwork_id=artwork_id
        )
        
        db.session.add(comment)
        artwork.comment_count += 1
        db.session.commit()
        
        return jsonify({
            'message': 'Comment added successfully',
            'comment': comment.to_dict()
        }), 201
        
    except Exception as e:
        logger.error(f"Error adding comment: {e}")
        db.session.rollback()
        return jsonify({'error': 'Failed to add comment'}), 500

@gallery_bp.route('/artwork/<artwork_id>/remix', methods=['POST'])
def remix_artwork(artwork_id):
    """Create a remix of an existing artwork"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'Authentication required'}), 401
        
        original_artwork = Artwork.query.get(artwork_id)
        if not original_artwork:
            return jsonify({'error': 'Original artwork not found'}), 404
        
        if not original_artwork.is_public:
            return jsonify({'error': 'Cannot remix private artwork'}), 403
        
        data = request.get_json()
        
        # Create new artwork as remix
        remix = Artwork(
            title=data.get('title', f"Remix of {original_artwork.title}"),
            description=data.get('description', f"Remixed from artwork by {original_artwork.creator.username}"),
            user_id=user_id,
            original_id=artwork_id,
            generation_type=data.get('generation_type', original_artwork.generation_type),
            style_id=data.get('style_id', original_artwork.style_id),
            parameters=data.get('parameters', original_artwork.parameters),
            processing_status='pending'
        )
        
        # Use original image as content for remix
        if original_artwork.generated_image_path:
            remix.original_image_path = original_artwork.generated_image_path
        
        db.session.add(remix)
        db.session.commit()
        
        return jsonify({
            'message': 'Remix created successfully',
            'remix_id': remix.id,
            'original_id': artwork_id
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating remix: {e}")
        db.session.rollback()
        return jsonify({'error': 'Failed to create remix'}), 500

@gallery_bp.route('/stats', methods=['GET'])
def get_gallery_stats():
    """Get gallery statistics"""
    try:
        stats = {
            'total_artworks': Artwork.query.filter_by(is_public=True, processing_status='completed').count(),
            'total_users': User.query.count(),
            'total_likes': Like.query.count(),
            'total_comments': Comment.query.count(),
            'total_styles': Style.query.filter_by(is_active=True).count()
        }
        
        return jsonify({'stats': stats}), 200
        
    except Exception as e:
        logger.error(f"Error fetching gallery stats: {e}")
        return jsonify({'error': 'Failed to fetch stats'}), 500

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import uuid

# Create db instance that will be imported by app.py
db = SQLAlchemy()

class User(db.Model):
    """User model for authentication and profiles"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    bio = db.Column(db.Text)
    avatar_url = db.Column(db.String(255))
    
    # Relationships
    artworks = db.relationship('Artwork', backref='creator', lazy=True, cascade='all, delete-orphan')
    likes = db.relationship('Like', backref='user', lazy=True, cascade='all, delete-orphan')
    comments = db.relationship('Comment', backref='author', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at.isoformat(),
            'bio': self.bio,
            'avatar_url': self.avatar_url,
            'artwork_count': len(self.artworks)
        }

class Style(db.Model):
    """Predefined style templates for style transfer"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    style_image_path = db.Column(db.String(255), nullable=False)
    thumbnail_path = db.Column(db.String(255))
    category = db.Column(db.String(50))  # e.g., 'classical', 'modern', 'abstract'
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    # Relationships
    artworks = db.relationship('Artwork', backref='style', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'style_image_path': self.style_image_path,
            'thumbnail_path': self.thumbnail_path,
            'category': self.category,
            'created_at': self.created_at.isoformat(),
            'is_active': self.is_active
        }

class Artwork(db.Model):
    """Generated artwork model"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # File paths
    original_image_path = db.Column(db.String(255), nullable=False)
    generated_image_path = db.Column(db.String(255), nullable=False)
    thumbnail_path = db.Column(db.String(255))
    
    # Generation parameters
    style_id = db.Column(db.String(36), db.ForeignKey('style.id'), nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    generation_type = db.Column(db.String(50), nullable=False)  # 'style_transfer', 'gan'
    parameters = db.Column(db.JSON)  # Store generation parameters as JSON
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_public = db.Column(db.Boolean, default=True)
    processing_status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    processing_time = db.Column(db.Float)  # seconds
    
    # Social features
    view_count = db.Column(db.Integer, default=0)
    like_count = db.Column(db.Integer, default=0)
    comment_count = db.Column(db.Integer, default=0)
    
    # Relationships
    likes = db.relationship('Like', backref='artwork', lazy=True, cascade='all, delete-orphan')
    comments = db.relationship('Comment', backref='artwork', lazy=True, cascade='all, delete-orphan')
    remixes = db.relationship('Artwork', backref='original', remote_side=[id])
    original_id = db.Column(db.String(36), db.ForeignKey('artwork.id'))  # For remixes
    
    def to_dict(self, include_user=True):
        result = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'original_image_path': self.original_image_path,
            'generated_image_path': self.generated_image_path,
            'thumbnail_path': self.thumbnail_path,
            'generation_type': self.generation_type,
            'parameters': self.parameters,
            'created_at': self.created_at.isoformat(),
            'is_public': self.is_public,
            'processing_status': self.processing_status,
            'processing_time': self.processing_time,
            'view_count': self.view_count,
            'like_count': self.like_count,
            'comment_count': self.comment_count,
            'original_id': self.original_id
        }
        
        if include_user and self.creator:
            result['creator'] = {
                'id': self.creator.id,
                'username': self.creator.username,
                'avatar_url': self.creator.avatar_url
            }
            
        if self.style:
            result['style'] = self.style.to_dict()
            
        return result

class Like(db.Model):
    """Like relationship between users and artworks"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    artwork_id = db.Column(db.String(36), db.ForeignKey('artwork.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Ensure unique likes per user per artwork
    __table_args__ = (db.UniqueConstraint('user_id', 'artwork_id', name='unique_user_artwork_like'),)

class Comment(db.Model):
    """Comments on artworks"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    content = db.Column(db.Text, nullable=False)
    user_id = db.Column(db.String(36), db.ForeignKey('user.id'), nullable=False)
    artwork_id = db.Column(db.String(36), db.ForeignKey('artwork.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'created_at': self.created_at.isoformat(),
            'author': {
                'id': self.author.id,
                'username': self.author.username,
                'avatar_url': self.author.avatar_url
            } if self.author else None
        }

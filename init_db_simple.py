#!/usr/bin/env python3
"""
Simple database initialization script without PIL dependency
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def initialize_database():
    """Initialize the database with tables and basic data"""
    try:
        from app import app
        from models import db, User, Style, Artwork
        
        print("Initializing database...")
        
        with app.app_context():
            print("Creating database tables...")
            db.create_all()
            
            print("Creating sample styles...")
            create_sample_styles(db, Style)
            
            print("Creating sample users...")
            create_sample_users(db, User)
            
            print("Database initialization completed successfully!")
            
    except Exception as e:
        print(f"Error initializing database: {e}")
        raise

def create_sample_styles(db, Style):
    """Create sample style entries without images"""
    styles_data = [
        {
            'name': 'Van Gogh Starry Night',
            'description': 'Swirling, dynamic brushstrokes inspired by <PERSON>',
            'category': 'classical'
        },
        {
            'name': 'Picasso Cubist',
            'description': 'Geometric, fragmented style of Picasso',
            'category': 'modern'
        },
        {
            'name': 'Monet Impressionist',
            'description': 'Soft, light-filled impressionist style',
            'category': 'classical'
        },
        {
            'name': 'Abstract Expressionist',
            'description': 'Bold, expressive abstract forms',
            'category': 'abstract'
        },
        {
            'name': 'Japanese Ukiyo-e',
            'description': 'Traditional Japanese woodblock print style',
            'category': 'traditional'
        },
        {
            'name': 'Watercolor Dreams',
            'description': 'Soft, flowing watercolor effects',
            'category': 'modern'
        }
    ]
    
    for style_data in styles_data:
        # Check if style already exists
        existing_style = Style.query.filter_by(name=style_data['name']).first()
        if not existing_style:
            # Create placeholder paths
            filename = style_data['name'].lower().replace(' ', '_') + '.jpg'
            filepath = os.path.join('static/styles', filename)
            thumb_filepath = os.path.join('static/styles', filename.replace('.jpg', '_thumb.jpg'))
            
            style = Style(
                name=style_data['name'],
                description=style_data['description'],
                style_image_path=filepath,
                thumbnail_path=thumb_filepath,
                category=style_data['category']
            )
            
            db.session.add(style)
            print(f"Created style: {style_data['name']}")
    
    db.session.commit()
    print("Sample styles created successfully")

def create_sample_users(db, User):
    """Create sample users"""
    users_data = [
        {
            'username': 'artist_demo',
            'email': '<EMAIL>',
            'bio': 'Demo artist account for testing the platform'
        },
        {
            'username': 'creative_soul',
            'email': '<EMAIL>',
            'bio': 'Passionate about AI-generated art and digital creativity'
        }
    ]
    
    for user_data in users_data:
        # Check if user already exists
        existing_user = User.query.filter_by(username=user_data['username']).first()
        if not existing_user:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                bio=user_data['bio']
            )
            db.session.add(user)
            print(f"Created user: {user_data['username']}")
    
    db.session.commit()
    print("Sample users created successfully")

if __name__ == '__main__':
    # Create directories
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('generated', exist_ok=True)
    os.makedirs('static/styles', exist_ok=True)
    
    initialize_database()
